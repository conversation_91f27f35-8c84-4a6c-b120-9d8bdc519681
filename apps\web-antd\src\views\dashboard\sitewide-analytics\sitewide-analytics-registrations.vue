<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import dayjs from 'dayjs';

// 定义 props
interface Props {
  sharedData?: any;
}

const props = defineProps<Props>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

// 图表数据
const chartData = ref<number[]>([]);
const xAxisData = ref<string[]>([]);
const currentYear = ref(dayjs().year());
const currentMonth = ref(dayjs().month() + 1);

// 使用共享数据更新图表
const updateWithData = (data: any) => {
  if (!data) return;

  console.log('Registrations component received data:', data);

  // 根据真实的数据结构处理每日注册数据
  const monthUserCount = data.monthUserCount || [];

  // 更新图表数据 - 使用 daily_login_count 作为注册量数据
  chartData.value = monthUserCount.map(
    (item: any) => item.daily_login_count || 0,
  );

  // 生成日期标签 - 从日期字符串中提取日期
  xAxisData.value = monthUserCount.map((item: any) => {
    const date = new Date(item.login_date);
    return `${date.getDate()}号`;
  });

  // 更新当前年月
  if (monthUserCount.length > 0) {
    const firstDate = new Date(monthUserCount[0].login_date);
    currentYear.value = firstDate.getFullYear();
    currentMonth.value = firstDate.getMonth() + 1;

    // 如果数据不足一个月，补充到当月天数
    const daysInMonth = dayjs(
      `${currentYear.value}-${currentMonth.value}`,
    ).daysInMonth();
    while (chartData.value.length < daysInMonth) {
      chartData.value.push(0);
    }
    while (xAxisData.value.length < daysInMonth) {
      xAxisData.value.push(`${xAxisData.value.length + 1}号`);
    }
  }

  renderChart();
};

// 监听共享数据变化
watch(
  () => props.sharedData,
  (newData) => {
    if (newData) {
      updateWithData(newData);
    }
  },
  { immediate: true },
);

// 渲染图表
const renderChart = () => {
  const maxValue = Math.max(...chartData.value);
  const yAxisMax = maxValue > 0 ? maxValue + 20 : 50;

  renderEcharts({
    grid: {
      bottom: 0,
      containLabel: true,
      left: '1%',
      right: '1%',
      top: '10%',
    },
    series: [
      {
        barMaxWidth: 80,
        data: chartData.value,
        type: 'bar',
        itemStyle: {
          color: '#52c41a', // 绿色，区别于访问量的蓝色
        },
      },
    ],
    tooltip: {
      axisPointer: {
        lineStyle: {
          width: 1,
        },
      },
      trigger: 'axis',
      formatter: (params: any) => {
        const dataIndex = params[0].dataIndex;
        const value = params[0].value;
        const date = xAxisData.value[dataIndex];
        return `
          <div>
            <div>${currentYear.value}年${currentMonth.value}月${date}</div>
            <div>用户注册量: ${value}</div>
          </div>
        `;
      },
    },
    xAxis: {
      data: xAxisData.value,
      type: 'category',
    },
    yAxis: {
      max: yAxisMax,
      splitNumber: 4,
      type: 'value',
    },
  });
};

// 暴露方法给父组件
defineExpose({
  updateWithData,
});
</script>

<template>
  <div>
    <!-- 图表 -->
    <EchartsUI ref="chartRef" />
  </div>
</template>
