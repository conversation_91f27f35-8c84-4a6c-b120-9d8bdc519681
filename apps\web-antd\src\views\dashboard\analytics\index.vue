<script lang="ts" setup>
import type { AnalysisOverviewItem } from '@vben/common-ui';

import { ref } from 'vue';

import { AnalysisOverview } from '@vben/common-ui';
import { SvgBellIcon, SvgCakeIcon, SvgCardIcon } from '@vben/icons';

import { DatePicker } from 'ant-design-vue';
import dayjs from 'dayjs';

import AnalyticsRegistrations from './analytics-registrations.vue';
import AnalyticsVisits from './analytics-visits.vue';

// 响应式数据
const overviewItems = ref<AnalysisOverviewItem[]>([]);

// 年月选择器状态 - 使用 dayjs 对象
const selectedDate = ref(dayjs());

// 子组件引用
const analyticsVisitsRef = ref();
const analyticsRegistrationsRef = ref();

// 处理子组件传递的数据
const handleDataUpdate = (data: any) => {
  console.log('Received data from child:', data);

  // 根据后端返回的数据结构更新概览数据
  overviewItems.value = [
    {
      icon: SvgCardIcon,
      title: '用户量',
      totalTitle: '总用户量',
      totalValue: data.userCount || 0,
      value: data.dailyUserCount || 0,
    },
    {
      icon: SvgCakeIcon,
      title: '访问量',
      totalTitle: '总访问量',
      totalValue: data.visitCount || 0,
      value: data.dailyVisitCount || 0,
    },

    {
      icon: SvgBellIcon,
      title: '智能体使用量',
      totalTitle: '总使用量',
      totalValue: data.usageCount || 0,
      value: data.dailyUsageCount || 0,
    },
  ];
};

// 日期改变时的处理
const onDateChange = (date: any) => {
  selectedDate.value = date || dayjs();
  // 通知子组件更新数据
  if (analyticsVisitsRef.value) {
    analyticsVisitsRef.value.updateData(
      selectedDate.value.year(),
      selectedDate.value.month() + 1,
    );
  }
  if (analyticsRegistrationsRef.value) {
    analyticsRegistrationsRef.value.updateData(
      selectedDate.value.year(),
      selectedDate.value.month() + 1,
    );
  }
};
</script>

<template>
  <div class="p-5">
    <AnalysisOverview :items="overviewItems || []" />

    <!-- 日注册量图表容器 -->
    <div class="card-box mt-5 w-full px-4 pb-5 pt-3">
      <!-- 标题和选择器在同一行 -->
      <div class="mb-4 flex items-center justify-between">
        <div class="text-lg font-medium">日注册量</div>
        <div class="flex items-center gap-2">
          <span class="text-sm">选择年月:</span>
          <DatePicker
            v-model:value="selectedDate"
            picker="month"
            size="small"
            placeholder="请选择年月"
            format="YYYY年MM月"
            @change="onDateChange"
          />
        </div>
      </div>

      <!-- 图表内容 -->
      <div class="pt-4">
        <AnalyticsRegistrations
          ref="analyticsRegistrationsRef"
          :selected-year="selectedDate.year()"
          :selected-month="selectedDate.month() + 1"
          @data-update="handleDataUpdate"
        />
      </div>
    </div>

    <!-- 日访问量图表容器 -->
    <div class="card-box mt-5 w-full px-4 pb-5 pt-3">
      <!-- 标题 -->
      <div class="mb-4 flex items-center justify-between">
        <div class="text-lg font-medium">日访问量</div>
      </div>

      <!-- 图表内容 -->
      <div class="pt-4">
        <AnalyticsVisits
          ref="analyticsVisitsRef"
          :selected-year="selectedDate.year()"
          :selected-month="selectedDate.month() + 1"
          @data-update="handleDataUpdate"
        />
      </div>
    </div>

    <div class="mt-5 w-full md:flex">
      <!-- <AnalysisChartCard class="mt-5 md:mr-4 md:mt-0 md:w-1/3" title="访问数量">
        <AnalyticsVisitsData />
      </AnalysisChartCard>
      <AnalysisChartCard class="mt-5 md:mr-4 md:mt-0 md:w-1/3" title="访问来源">
        <AnalyticsVisitsSource />
      </AnalysisChartCard>
      <AnalysisChartCard class="mt-5 md:mt-0 md:w-1/3" title="访问来源">
        <AnalyticsVisitsSales />
      </AnalysisChartCard> -->
    </div>
  </div>
</template>
