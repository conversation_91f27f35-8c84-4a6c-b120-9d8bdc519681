<script lang="ts" setup>
import type { AnalysisOverviewItem } from '@vben/common-ui';

import { onMounted, ref } from 'vue';

import { AnalysisOverview } from '@vben/common-ui';
import {
  SvgBellIcon,
  SvgCakeIcon,
  SvgCardIcon,
  SvgDownloadIcon,
} from '@vben/icons';

import { DatePicker } from 'ant-design-vue';
import dayjs from 'dayjs';

import { messageListAdmin } from '#/api/analytics';

import SitewideAnalyticsRegistrations from './sitewide-analytics-registrations.vue';
import SitewideAnalyticsVisits from './sitewide-analytics-visits.vue';

// 响应式数据
const overviewItems = ref<AnalysisOverviewItem[]>([]);

// 年月选择器状态 - 使用 dayjs 对象
const selectedDate = ref(dayjs());

// 子组件引用
const analyticsVisitsRef = ref();
const analyticsRegistrationsRef = ref();

// 共享的数据状态
const sharedData = ref<any>(null);

// 加载数据的方法
const loadData = async (year?: number, month?: number) => {
  const targetYear = year || selectedDate.value.year();
  const targetMonth = month || selectedDate.value.month() + 1;

  try {
    // 只请求一次接口
    const response = await messageListAdmin({
      year: targetYear,
      month: targetMonth,
    });

    console.log('Sitewide API Response:', response);

    // 保存共享数据
    sharedData.value = response;

    // 更新概览数据
    overviewItems.value = [
      {
        icon: SvgCardIcon,
        title: '用户量',
        totalTitle: '总用户量',
        totalValue: response.userCount || 0,
        value: response.dailyUserCount || 0,
      },
      {
        icon: SvgCakeIcon,
        title: '访问量',
        totalTitle: '总访问量',
        totalValue: response.visitCount || 0,
        value: response.dailyVisitCount || 0,
      },
      {
        icon: SvgDownloadIcon,
        title: '租户量',
        totalTitle: '总租户量',
        totalValue: response.tenantCount || 0,
        value: response.dailyTenantCount || 0,
      },
      {
        icon: SvgBellIcon,
        title: '智能体使用量',
        totalTitle: '总智能体使用量',
        totalValue: response.usageCount || 0,
        value: response.dailyUsageCount || 0,
      },
    ];

    // 通知子组件更新数据
    if (analyticsVisitsRef.value) {
      analyticsVisitsRef.value.updateWithData(response);
    }
    if (analyticsRegistrationsRef.value) {
      analyticsRegistrationsRef.value.updateWithData(response);
    }
  } catch (error) {
    console.error('Failed to load analytics data:', error);
  }
};

// 日期改变时的处理
const onDateChange = (date: any) => {
  selectedDate.value = date || dayjs();
  // 重新加载数据
  loadData(selectedDate.value.year(), selectedDate.value.month() + 1);
};

// 组件挂载时加载初始数据
onMounted(() => {
  loadData();
});
</script>

<template>
  <div class="p-5">
    <AnalysisOverview :items="overviewItems || []" />

    <!-- 日注册量图表容器 -->
    <div class="card-box mt-5 w-full px-4 pb-5 pt-3">
      <!-- 标题和选择器在同一行 -->
      <div class="mb-4 flex items-center justify-between">
        <div class="text-lg font-medium">日注册量</div>
        <div class="flex items-center gap-2">
          <span class="text-sm">选择年月:</span>
          <DatePicker
            v-model:value="selectedDate"
            picker="month"
            size="small"
            placeholder="请选择年月"
            format="YYYY年MM月"
            @change="onDateChange"
          />
        </div>
      </div>

      <!-- 图表内容 -->
      <div class="pt-4">
        <SitewideAnalyticsRegistrations
          ref="analyticsRegistrationsRef"
          :shared-data="sharedData"
        />
      </div>
    </div>

    <!-- 日访问量图表容器 -->
    <div class="card-box mt-5 w-full px-4 pb-5 pt-3">
      <!-- 标题 -->
      <div class="mb-4 flex items-center justify-between">
        <div class="text-lg font-medium">日访问量</div>
      </div>

      <!-- 图表内容 -->
      <div class="pt-4">
        <SitewideAnalyticsVisits
          ref="analyticsVisitsRef"
          :shared-data="sharedData"
        />
      </div>
    </div>

    <div class="mt-5 w-full md:flex">
      <!-- <AnalysisChartCard class="mt-5 md:mr-4 md:mt-0 md:w-1/3" title="访问数量">
        <AnalyticsVisitsData />
      </AnalysisChartCard>
      <AnalysisChartCard class="mt-5 md:mr-4 md:mt-0 md:w-1/3" title="访问来源">
        <AnalyticsVisitsSource />
      </AnalysisChartCard>
      <AnalysisChartCard class="mt-5 md:mt-0 md:w-1/3" title="访问来源">
        <AnalyticsVisitsSales />
      </AnalysisChartCard> -->
    </div>
  </div>
</template>
