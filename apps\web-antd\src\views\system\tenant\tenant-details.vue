<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getUserInfoApi } from '#/api/core/user';
import { tenantUpdate, tenantuserInfo } from '#/api/system/tenant';

import { drawerSchema } from './data';

const loading = ref(false);
const currentTenantId = ref<string>('');

// 处理租户logo文件上传
const logoFileList = ref<any[]>([]);
const logoFile = ref<File | null>(null);

// 处理租户首页logo文件上传
const homeLogoFileList = ref<any[]>([]);
const homeLogoFile = ref<File | null>(null);

// 处理租户移动端logo文件上传
const mobileLogoFileList = ref<any[]>([]);
const mobileLogoFile = ref<File | null>(null);

// 创建表单配置，过滤掉管理员信息和租户设置部分
const detailsSchema = computed(() => {
  return drawerSchema().filter((item) => {
    // 过滤掉管理员信息和租户设置相关字段
    const excludeFields = [
      'username',
      'password',
      'divider2', // 管理员信息分割线
      'divider3', // 租户设置分割线
      'packageId', // 租户套餐
      'expireTime', // 过期时间
      'accountCount', // 用户数量
      'domain', // 绑定域名
    ];
    return !excludeFields.includes(item.fieldName);
  });
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    formItemClass: 'col-span-2',
    labelWidth: 120,
    componentProps: {
      class: 'w-full',
    },
  },
  schema: detailsSchema.value,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

// 获取当前用户信息并加载租户数据
async function loadTenantData() {
  try {
    loading.value = true;

    // 1. 获取当前用户信息
    const userInfo = await getUserInfoApi();
    if (!userInfo?.user?.tenantId) {
      message.error('无法获取当前用户的租户信息');
      return;
    }

    currentTenantId.value = userInfo.user.tenantId;

    // 2. 配置表单组件
    await updateFormComponents();

    // 4. 获取租户数据
    const tenantData = await tenantuserInfo(currentTenantId.value);

    // 5. 设置表单值
    await formApi.setValues(tenantData);

    // 6. 设置logo预览（如果有logo）
    if (tenantData?.tenantLogo) {
      await setupLogoPreview('tenantLogo', tenantData.tenantLogo);
    }
    if (tenantData?.homeLogo) {
      await setupLogoPreview('homeLogo', tenantData.homeLogo);
    }
    if (tenantData?.mobileLogo) {
      await setupLogoPreview('mobileLogo', tenantData.mobileLogo);
    }

    // 7. 等待一下确保所有更新完成
    await new Promise((resolve) => setTimeout(resolve, 200));
  } catch (error) {
    console.error('加载租户数据失败:', error);
    message.error('加载租户数据失败');
  } finally {
    loading.value = false;
  }
}

// 更新表单组件配置
async function updateFormComponents() {
  formApi.updateSchema([
    {
      fieldName: 'tenantLogoUpload',
      componentProps: {
        maxCount: 1,
        multiple: false,
        listType: 'picture-card',
        accept: 'image/*',
        showUploadList: true,
        beforeUpload: () => false, // 阻止自动上传
        onChange: (info: any) => {
          handleLogoChange('tenantLogo', info);
        },
        fileList: logoFileList.value,
      },
    },
    {
      fieldName: 'homeLogoUpload',
      componentProps: {
        maxCount: 1,
        multiple: false,
        listType: 'picture-card',
        accept: 'image/*',
        showUploadList: true,
        beforeUpload: () => false, // 阻止自动上传
        onChange: (info: any) => {
          handleLogoChange('homeLogo', info);
        },
        fileList: homeLogoFileList.value,
      },
    },
    {
      fieldName: 'mobileLogoUpload',
      componentProps: {
        maxCount: 1,
        multiple: false,
        listType: 'picture-card',
        accept: 'image/*',
        showUploadList: true,
        beforeUpload: () => false, // 阻止自动上传
        onChange: (info: any) => {
          handleLogoChange('mobileLogo', info);
        },
        fileList: mobileLogoFileList.value,
      },
    },
  ]);
}

// 处理Logo文件变更
function handleLogoChange(
  logoType: 'homeLogo' | 'mobileLogo' | 'tenantLogo',
  info: any,
) {
  switch (logoType) {
    case 'homeLogo': {
      // 更新文件列表
      homeLogoFileList.value = [...info.fileList];
      // 如果有新文件，保存File对象
      if (info.fileList.length > 0) {
        const latestFile = info.fileList[info.fileList.length - 1];
        if (latestFile.originFileObj) {
          homeLogoFile.value = latestFile.originFileObj;
        }
      } else {
        homeLogoFile.value = null;
      }
      break;
    }
    case 'mobileLogo': {
      // 更新文件列表
      mobileLogoFileList.value = [...info.fileList];
      // 如果有新文件，保存File对象
      if (info.fileList.length > 0) {
        const latestFile = info.fileList[info.fileList.length - 1];
        if (latestFile.originFileObj) {
          mobileLogoFile.value = latestFile.originFileObj;
        }
      } else {
        mobileLogoFile.value = null;
      }
      break;
    }
    case 'tenantLogo': {
      // 更新文件列表
      logoFileList.value = [...info.fileList];
      // 如果有新文件，保存File对象
      if (info.fileList.length > 0) {
        const latestFile = info.fileList[info.fileList.length - 1];
        if (latestFile.originFileObj) {
          logoFile.value = latestFile.originFileObj;
        }
      } else {
        logoFile.value = null;
      }
      break;
    }
    // No default
  }
}

// 设置Logo预览
async function setupLogoPreview(
  logoType: 'homeLogo' | 'mobileLogo' | 'tenantLogo',
  logoUrl: string,
) {
  try {
    if (!logoUrl) {
      return;
    }

    // 设置预览文件列表
    const previewFile = {
      uid: `-${logoType}`,
      name: `${logoType}.png`,
      status: 'done' as const,
      url: logoUrl,
      thumbUrl: logoUrl,
      response: { url: logoUrl },
    };

    switch (logoType) {
      case 'homeLogo': {
        // 清空文件对象，因为这是预览已有的图片
        homeLogoFile.value = null;
        homeLogoFileList.value = [previewFile];
        // 直接设置表单字段的值为文件列表
        await formApi.setFieldValue('homeLogoUpload', homeLogoFileList.value);
        break;
      }
      case 'mobileLogo': {
        // 清空文件对象，因为这是预览已有的图片
        mobileLogoFile.value = null;
        mobileLogoFileList.value = [previewFile];
        // 直接设置表单字段的值为文件列表
        await formApi.setFieldValue(
          'mobileLogoUpload',
          mobileLogoFileList.value,
        );
        break;
      }
      case 'tenantLogo': {
        // 清空文件对象，因为这是预览已有的图片
        logoFile.value = null;
        logoFileList.value = [previewFile];
        // 直接设置表单字段的值为文件列表
        await formApi.setFieldValue('tenantLogoUpload', logoFileList.value);
        break;
      }
      // No default
    }

    // 等待一下确保状态更新
    await new Promise((resolve) => setTimeout(resolve, 200));

    // 再次强制更新组件配置
    await updateFormComponents();
  } catch (error) {
    console.error(`设置${logoType}预览失败:`, error);
  }
}

// 移除套餐选择器相关函数

// 保存租户信息
async function handleSave() {
  try {
    loading.value = true;
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());

    // 移除Upload组件的中间字段
    delete data.tenantLogoUpload;
    delete data.homeLogoUpload;
    delete data.mobileLogoUpload;

    // 创建FormData对象
    const formData = new FormData();

    // 将业务数据包装到bo对象中
    const boData: Record<string, any> = { ...data };

    // 如果没有新文件上传，保留原有logo URL
    if (!logoFile.value && data.tenantLogo) {
      boData.tenantLogo = data.tenantLogo;
    }
    if (!homeLogoFile.value && data.homeLogo) {
      boData.homeLogo = data.homeLogo;
    }
    if (!mobileLogoFile.value && data.mobileLogo) {
      boData.mobileLogo = data.mobileLogo;
    }

    // 将bo对象转为JSON字符串并添加到FormData
    formData.append(
      'bo',
      new Blob([JSON.stringify(boData)], { type: 'application/json' }),
    );

    // 如果有新文件，添加到FormData
    if (logoFile.value) {
      formData.append('tenantLogo', logoFile.value);
    }
    if (homeLogoFile.value) {
      formData.append('homeLogo', homeLogoFile.value);
    }
    if (mobileLogoFile.value) {
      formData.append('mobileLogo', mobileLogoFile.value);
    }

    // 提交表单数据
    await tenantUpdate(formData);

    message.success('保存成功');

    // 重新加载数据
    await loadTenantData();
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败');
  } finally {
    loading.value = false;
  }
}

// 重置表单
async function handleReset() {
  await loadTenantData();
  message.info('已重置为原始数据');
}

// 页面加载时获取数据
onMounted(() => {
  loadTenantData();
});
</script>

<template>
  <Page :auto-content-height="true" :loading="loading">
    <div class="mx-auto max-w-4xl">
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">租户信息管理</h1>
        <p class="mt-2 text-sm text-gray-600">
          管理当前租户的基本信息、企业信息和相关设置
        </p>
      </div>

      <div class="rounded-lg bg-white p-6 shadow">
        <BasicForm />

        <div class="mt-6 flex justify-end space-x-3">
          <a-button @click="handleReset">
            {{ $t('pages.common.reset') }}
          </a-button>
          <a-button type="primary" :loading="loading" @click="handleSave">
            {{ $t('pages.common.save') }}
          </a-button>
        </div>
      </div>
    </div>
  </Page>
</template>

<style lang="scss" scoped>
:deep(.ant-divider) {
  margin: 16px 0;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-upload-list-picture-card .ant-upload-list-item) {
  width: 104px;
  height: 104px;
}

:deep(.ant-upload-select-picture-card) {
  width: 104px;
  height: 104px;
}
</style>
